require_relative "rendered_template_content"

module Communications
  module Email
    class StoreTranslationContent
      def initialize(rendered_template_content: RenderedTemplateContent.new, correlation_id: nil)
        @rendered_template_content = rendered_template_content
        @correlation_id = correlation_id
      end

      # @param template [CommunicationTemplate] the template to be rendered
      # @param response_id [String, BSON::ObjectId] the ID of the response this translation belongs to
      # @param variable [<Hash<locale, variables>] the resolved html variables for this template
      # @param locales [Array<String>] the set of locales to store translations for
      def call(template:, response_id:, variables:, locales:)
        response = Response.includes(:survey).find(response_id)
        survey = response.survey

        rendered_templates = locales.each_with_object({}) { |translation_locale, memo|
          # render a template for each locale
          localized_variables = variables.fetch(translation_locale, nil)
          translation_status, translation_result = @rendered_template_content.call(
            template: template,
            response: response,
            locale: translation_locale,
            survey: survey,
            variables: localized_variables
          )

          memo[translation_locale] = [translation_status, translation_result]
        }

        subject_translations = {}
        message_translations = {}
        rendered_templates.each do |locale, (status, result)|
          next unless status == :ok

          subject_translations[locale] = result["subject"]
          message_translations[locale] = result["message"]
        end

        sent_email_translations_params = {
          response_id: response.id.to_s,
          response_aggregate_id: response.aggregate_id.to_s,
          account_id: survey.account_id.to_s,
          account_aggregate_id: survey.account.aggregate_id.to_s,
          template_type: template.type,
          subject_translations: subject_translations,
          html_content_translations: message_translations,
          created_at: Time.zone.now
        }

        Communications::Commands::UpsertSentEmailTranslation.new.call(
          sent_email_translations_params
        ).or do |e|
          Bugsnag.notify(e)
          Splunk::Logger.new(Rails.logger).log(
            app: "murmur",
            module: "communications.email.store_translation_content",
            error: e,
            correlation_id: @correlation_id
          )
        end
      end
    end
  end
end
