require_relative "rendered_template_content"
require_relative "store_translation_content"
require_relative "find_communication_template"
require_relative "communication_variables"

module Communications
  module Email
    class SendEmail
      def initialize(
        rendered_template_content: RenderedTemplateContent.new,
        send_email_job: ::Email::SendEmailJob,
        job_queue: Delayed::Job,
        find_communication_template: Communications::Email::FindCommunicationTemplate.new,
        correlation_id: nil,
        logger: Splunk::Logger.new(Rails.logger)
      )
        @rendered_template_content = rendered_template_content
        @send_email_job = send_email_job
        @job_queue = job_queue
        @find_communication_template = find_communication_template
        @correlation_id = correlation_id
        @logger = logger
      end

      def call(account:, survey:, template_type:, recipient:, locale:, response: nil, additional_fields: {}, conversation: nil, variables_resolver: nil, enqueue_options: {})
        variables = variables_resolver&.call(locale)&.fetch(:html)

        unless survey.improved_communications_enabled?
          return LegacySendEmail.new(correlation_id: @correlation_id).call(
            recipient: recipient,
            survey: survey,
            response: response,
            template_type: template_type,
            locale: locale,
            additional_fields: additional_fields,
            variables: variables,
            conversation: conversation
          )
        end

        template = @find_communication_template.call(
          survey_id: survey.id,
          template_type: template_type
        )

        status, result = @rendered_template_content.call(
          template: template,
          response: response,
          locale: locale,
          survey: survey,
          variables: variables
        )

        unless status == :ok
          log(error: result)
          return [:error, result]
        end

        send_email_job = @send_email_job.new(
          subject: result["subject"],
          to: recipient.email,
          locale: locale,
          html_content: result["message"],
          support_email: survey.support_email,
          account_id: account.id,
          account_subdomain: account.subdomain,
          account_email_sender: account.email_sender,
          correlation_id: @correlation_id
        )

        @job_queue.enqueue(send_email_job, enqueue_options)

        if response && survey.create_sent_comms_translations?
          RenderAndSaveTranslations.new.call(
            survey: survey,
            response: response,
            account: account,
            template: template,
            locale: locale,
            variables_resolver: variables_resolver
          )
        end

        [:ok, ""]
      end

      private

      def log(**attrs)
        @logger.log(
          app: "murmur",
          module: "communications.email.send_email",
          correlation_id: @correlation_id,
          **attrs
        )
      end
    end

    require_relative "logos"
    require_relative "../confidentiality_url"
    class LegacySendEmail
      def initialize(correlation_id: nil, logger: Splunk::Logger.new(Rails.logger))
        @find_communication_template = FindCommunicationTemplate.new
        @communication_variables = CommunicationVariables.new
        @confidentiality_url = ::Communications::ConfidentialityUrl.new
        @logos = Logos.new
        @correlation_id = correlation_id
        @logger = logger
      end

      def call(survey:, recipient:, template_type:, locale:, response:, additional_fields:, conversation:, variables:)
        template = @find_communication_template.call(
          survey_id: survey.id,
          template_type: template_type
        )
        unless template
          error = "missing_template"
          log(error: error)
          return [:error, error] unless template
        end

        # We allow for `variables` to be passed in as for bulk 360 communications, `response` is nil, and therefore
        # CommunicationVariables cannot be used.
        variables ||= begin
          confidentiality_url = @confidentiality_url.call(
            response: response,
            survey: survey,
            account: survey.account
          )

          @communication_variables.call(
            survey: survey,
            response: response,
            locale: locale
          ).merge(additional_fields)
            .merge(
              confidentiality_link: confidentiality_url
            )
        end

        if survey.three_sixty?
          layout_name = "styled_layout_en"
          layout_variables = @logos.call(survey)
        else
          layout_name = nil
          layout_variables = {}
        end

        email_comm = template.new_email_communication(
          recipient,
          field_values: variables,
          locales: survey.supported_locales,
          default_locale: locale,
          layout_template_name: layout_name,
          layout_template_variables: layout_variables
        )

        email_comm.conversation = conversation
        email_comm.account = survey.account
        email_comm.save!
        email_comm.deliver!

        [:ok, ""]
      rescue => e
        log(error: e)
        raise e
      end

      private

      def log(**attrs)
        @logger.log(
          app: "murmur",
          module: "communications.email.send_email",
          correlation_id: @correlation_id,
          **attrs
        )
      end
    end

    class RenderAndSaveTranslations
      TRANSLATION_RENDERING_PRIORITY = 9

      def initialize(store_translation_content: StoreTranslationContent.new)
        @store_translation_content = store_translation_content
      end

      def call(survey:, response:, account:, template:, locale:, variables_resolver:)
        # Variables for bundled communications
        resolved_ongoing_variables = survey.supported_locales.each_with_object({}) { |translation_locale, memo|
          memo[translation_locale] = variables_resolver&.call(translation_locale)&.fetch(:html)
        }

        english_locale = ["en"]
        non_english_locales = survey.supported_locales - english_locale

        # Render and store the English version in line so we have a fallback
        @store_translation_content.call(
          template: template,
          response_id: response.id,
          variables: resolved_ongoing_variables,
          locales: english_locale
        )

        # Render remaining locales in the background if we have any
        if non_english_locales.present?
          @store_translation_content.delay(priority: TRANSLATION_RENDERING_PRIORITY).call(
            template: template,
            response_id: response.id,
            variables: resolved_ongoing_variables,
            locales: non_english_locales
          )
        end
      end
    end
  end
end
