module Jobs
  module SurveyLaunch
    class BatchResponseCreationJob < QueueableJob
      GeneratedCorrelationID = -> { SecureRandom.uuid }

      def initialize(survey_id:, user_ids:, survey_invite_job_id:, executor_id:, correlation_id: nil)
        @survey_id = survey_id
        @user_ids = user_ids
        @survey_invite_job_id = survey_invite_job_id
        @executor_id = executor_id
        @correlation_id = correlation_id
      end

      def perform
        Datadog.tracer.trace("SurveyLaunch", {service: "murmur", resource: "Jobs::SurveyLaunch::BatchResponseCreationJob"}) do |span|
          span.set_tag("survey_id", @survey_id)
          span.set_tag("correlation_id", correlation_id)
          # we need to calculate this before creating responses as that will affect the calculation of the number of
          # "skipped" responses, which in turn incorrectly updates the completed counts on the SurveyInviteJob
          responses_to_reset = deleted_responses
          created_responses = batch_create_responses

          if survey.config(Configs::SURVEY_CAN_SEND_SMS) == Configs::ENABLED
            response_aggregate_ids = created_responses.map(&:aggregate_id)
            Responses::Commands::BatchCreateResponseShortCodes.new.call(
              response_aggregate_ids: response_aggregate_ids, survey_aggregate_id: survey.aggregate_id
            )
          end

          # TODO: cleanup: Event sourced flow is now deprecated
          if survey.event_sourced_survey_launch?
            splunk_logger.log(
              app: "murmur",
              msg: "jobs.survey_launch.batch_response_creation_job",
              module: self.class.name,
              correlation_id: correlation_id,
              survey_invite_job_id: survey_invite_job_id.to_s
            )

            Participants::Commands::BulkInviteParticipants.new.call(
              survey_id: survey_id,
              responses: responses_to_reset + created_responses,
              executor: executor,
              correlation_id: correlation_id,
              survey_invite_job: survey_invite_job
            )
          else
            reset_responses = batch_reset_responses(responses_to_reset)
            survey_invite_job.responses_created(
              created: created_responses.map(&:id),
              skipped: skipped_response_ids,
              reset: reset_responses.map(&:id)
            )
          end
        end
      end

      private

      attr_reader :survey_id, :user_ids, :survey_invite_job_id, :executor_id

      def correlation_id
        @correlation_id ||= GeneratedCorrelationID.call
      end

      def batch_reset_responses(responses)
        reset_user_ids = responses.map(&:user_id)
        response_service.batch_reset_responses(survey: survey, user_ids: reset_user_ids)
      end

      def batch_create_responses
        create_user_ids = user_ids - existing_responses.map(&:user_id)
        response_service.batch_create_responses(
          survey: survey,
          user_ids: create_user_ids,
          event_sourced_survey_launch: survey.event_sourced_survey_launch?
        )
      end

      def executor
        @executor ||= User.find(executor_id)
      end

      def survey
        @survey ||= Survey.find(survey_id)
      end

      def skipped_response_ids
        responses_to_skip.map(&:id)
      end

      def existing_responses
        @existing_responses ||= survey.responses.in(user_id: user_ids).only(:status, :demonstration, :user_id, :participant_id)
      end

      def responses_to_skip
        @responses_to_skip ||= existing_responses.real.to_a
      end

      def deleted_responses
        @deleted_responses ||= existing_responses.to_a - responses_to_skip
      end

      def survey_invite_job
        Jobs::SurveyLaunch::SurveyInviteJob.find(survey_invite_job_id)
      end

      def response_service
        @response_service ||= Services::ResponseService.new
      end

      def splunk_logger
        @splunk_logger ||= Splunk::Logger.new(Rails.logger)
      end
    end
  end
end
