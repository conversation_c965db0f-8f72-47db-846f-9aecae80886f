module Jobs
  module SurveyLaunch
    class BatchEmailSendJob < QueueableJob
      def initialize(survey_id:, response_ids:, survey_invite_job_id:, correlation_id: nil)
        @survey_id = survey_id
        @response_ids = response_ids
        @survey_invite_job_id = survey_invite_job_id
        @correlation_id = correlation_id
      end

      def perform
        Datadog.tracer.trace("SurveyLaunch", {service: "murmur", resource: "Jobs::SurveyLaunch::BatchEmailSendJob"}) do |span|
          span.set_tag("survey_id", @survey_id)
          span.set_tag("correlation_id", @correlation_id)

          Jobs::Throttler.for_emails(entries: responses_to_invite, survey: survey, throttle_rate: throttle_rate).run do |response, run_at|
            invite_participant(response, run_at)
            begin
              log_amplitude_invite_sent(response)
            rescue
              next
            end
          end

          survey_invite_job.update_progress(@response_ids.count)
        end
      end

      private

      attr_reader :survey_id, :response_ids, :survey_invite_job_id

      def survey
        @survey ||= Survey.find(survey_id)
      end

      def responses_to_invite
        @_responses_to_invite ||= survey.responses.where(:id.in => response_ids).select(&:send_notifications?)
      end

      def priority
        @priority ||= survey_invite_job.scheduling_priority
      end

      def invite_participant(response, run_at)
        response.current_conversation.trigger(:invite, max_attempts: 1, priority: priority, run_at: run_at, causation_id: survey_invite_job_id, correlation_id: @correlation_id)
      end

      def survey_invite_job
        Jobs::SurveyLaunch::SurveyInviteJob.find(survey_invite_job_id)
      end

      def throttle_rate
        throttle_survey_config = survey.config(Configs::THROTTLE_SURVEY_INVITATIONS_PER_SECOND)
        return unless throttle_survey_config&.nonzero?

        throttle_survey_config / survey_invite_job.number_of_batches.to_f
      end

      def log_amplitude_invite_sent(response)
        survey_aggregate_id = survey.aggregate_id
        Analytics.log_event(
          user_id: response.user.aggregate_id,
          event_type: "Platform Communication Received",
          event_properties: {
            Analytics::EVENT_PROPERTY_MEDIUM => Analytics::MEDIUM_EMAIL,
            Analytics::EVENT_PROPERTY_COMMUNICATION_NAME => Analytics::COMMUNICATION_NAME_SURVEY_CAPTURE
          },
          group_properties: [Analytics::ACCOUNT_GROUP.call(response.user.account.aggregate_id), Analytics::SURVEY_GROUP.call(survey_aggregate_id)]
        )
      end
    end
  end
end
