module Jobs
  module SurveyLaunch
    class SurveyInviteJob < BackgroundJob
      # all jobs spawned from this job are set at a slightly lower priority to
      # allow for shorter-running jobs to take precedence over this; all increments
      # are based on this baseline
      BASELINE_SCHEDULING_PRIORITY = Delayed::Worker.default_priority + 1

      CLIENT_CHURNED_MESSAGE = "Client churned; Postponing delivery."

      has_and_belongs_to_many :participants, class_name: "Person", inverse_of: nil

      field :invalid_participant_ids, type: Array, default: []
      field :completed_participants_count, type: Integer, default: 0

      # scheduling priority is determined by the number of participants;
      field :scheduling_priority, type: Integer, default: BASELINE_SCHEDULING_PRIORITY

      LOGGER = Splunk::Logger.new(Rails.logger)

      def queue
        uninterruptible_queue
      end

      def max_attempts
        1
      end

      def correlation_id
        if self[:correlation_id].nil?
          self[:correlation_id] = SecureRandom.uuid
        end

        self[:correlation_id]
      end

      def run
        Datadog.tracer.trace("SurveyLaunch", {service: "murmur", resource: "Jobs::SurveyLaunch::SurveyInviteJob"}) do |span|
          span.set_tag("correlation_id", correlation_id)
          span.set_tag("survey_id", survey_id)

          fail "Survey missing! #{survey_id}" if survey_removed_from_database?
          fail "Survey Period missing! (Survey ID: #{survey_id})" if survey_periods_missing?
          fail "Survey #{survey_id} is not a snapshot" if survey.present? && !survey.snapshot?

          if account.churned?
            postpone! CLIENT_CHURNED_MESSAGE
            "Launch postponed"
          elsif can_launch?
            update_attributes(status: :pending, started_at: Time.now.utc)
            launch_survey
            "Launch scheduled"
          end
        end
      rescue => e
        log(error: e)
        fail e
      end

      def launch_survey
        begin
          Surveys::Commands::LaunchSurvey.new.call(
            survey: survey,
            executor_user_id: executor.aggregate_id,
            account_id: account.aggregate_id,
            correlation_id: correlation_id
          )
        rescue EventFramework::Repository::AggregateAlreadyExists
          # Since this code gets re-used post-launch we need to rescue this here
          log(error: "Survey aggregate already exists")
        end

        self.participant_ids = participant_ids_to_invite
        self.scheduling_priority = Communications::Support::CalculateCommunicationJobPriority.call(participant_ids.count)
        log_job_stat

        if participant_ids.count.zero?
          log(message: "No participants to invite")
          complete_job
        end

        save!

        participant_ids.in_groups_of(batch_size, false) do |batch|
          batch_responses(batch)
        end
      end

      def can_launch?
        survey.present? && !(survey.inactive? || survey.deleted?) && survey.snapshot?
      end

      def survey_removed_from_database?
        (survey.nil? || survey.deleted?) && !Survey.deleted.where(_id: survey_id).exists?
      end

      def survey_periods_missing?
        survey.present? && survey.survey_periods.blank?
      end

      def participant_count
        count = participant_ids.count
        count = participant_ids_to_invite.count if count == 0 && result.nil?
        count
      end

      # NOTE: this code isn't run as part of the initial delayed run of
      # SurveyInviteJob, but is called as part of the subsequently generated and
      # run BatchResponseCreationJob.
      def responses_created(created:, reset:, skipped:)
        skipped_participant_count = skipped.count

        update_progress(skipped_participant_count)

        log(message: "#{created.count} responses created, #{skipped_participant_count} skipped, #{reset.count} reset")

        participants_to_invite = [created, reset].flatten
        if participants_to_invite.empty?
          log(message: "No participants to invite")
          return
        end

        # We dont want BatchEmailSendJob to be killed in flight.
        # this avoid duplicated emails scheduled for customers.
        # Also put it in a higher priority than the bulk created as it is faster
        BatchEmailSendJob
          .prioritize(scheduling_priority - 1)
          .with_causation_id(id)
          .on_queue(uninterruptible_queue)
          .max_attempts(1)
          .enqueue(
            survey_id: survey.id,
            response_ids: participants_to_invite,
            survey_invite_job_id: id,
            correlation_id: correlation_id
          )
      end

      def update_progress(completed_participant_batch_count)
        expected_participants = participant_ids.count

        inc(completed_participants_count: completed_participant_batch_count, complete: (completed_participant_batch_count / expected_participants.to_f))
        reload
        if expected_participants == completed_participants_count
          Hierarchies::Commands::CloneAccountHierarchiesIntoSurvey.new.call(survey: survey, remove_inactive_managers: true)
          complete_job
        end
      end

      def number_of_batches
        (participants.count / batch_size.to_f).ceil
      end

      def job_stats
        @_job_stats ||= Delayed::Job.where(causation_id: id).distinct(:queue).each_with_object({}) { |queue, acc|
          acc[queue.to_sym] = Delayed::Job.where(causation_id: id, queue: queue).count
        }
      end

      # @logger needs to be overridden when the update_progress callback is being called from outside of rails
      def set_logger(logger)
        @logger = logger
      end

      private

      def admin_grants
        survey.survey_admin_grants.active
      end

      def batch_responses(batch)
        BatchResponseCreationJob
          .prioritize(scheduling_priority)
          .with_causation_id(id)
          .on_queue(uninterruptible_queue)
          .enqueue(
            survey_id: survey.id,
            user_ids: batch,
            survey_invite_job_id: id,
            executor_id: executor.id,
            correlation_id: correlation_id
          )
      end

      def uninterruptible_queue
        # these jobs are not idempotent. they leaf lots of state breadcrumbs when running
        # workers on this queue should not be interrupted
        "uninterruptible_short"
      end

      def batch_size
        @batch_size ||= survey.config(Configs::SURVEY_INVITE_BATCH_SIZE)
      end

      def complete_job
        update_attributes!(status: :complete, completed_at: Time.now.utc, complete: 1.0)
        log_job_stat
      end

      def participant_ids_to_invite
        previously_invited_ids = survey.responses.real.pluck(:user_id)
        all_survey_participant_ids = survey.active_participants.pluck(:id)

        all_survey_participant_ids - previously_invited_ids
      end

      def calculate_duration
        completed_at.nil? || started_at.nil? ? "n/a" : (completed_at - started_at).round(1)
      end

      def logger
        @logger ||= LOGGER
      end

      def log_job_stat
        log(
          event: :batched_survey_launch,
          status: status,
          job_id: id,
          survey_id: survey.id&.to_s,
          account_subdomain: account&.subdomain,
          participant_count: participant_ids&.count.to_s,
          time_taken: calculate_duration.to_s,
          event_sourced: survey.event_sourced?.to_s,
          event_sourced_survey_launch: survey.event_sourced_survey_launch?.to_s
        )
      end

      def log(**attrs)
        logger.log(
          app: "murmur",
          module: "jobs.survey_launch.survey_invite_job",
          correlation_id: correlation_id,
          **attrs
        )
      end
    end
  end
end
