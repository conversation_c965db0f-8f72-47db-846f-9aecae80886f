module Email
  # This class implementes the methods required by Delayed::Job for
  # a custom job. See https://github.com/collectiveidea/delayed_job/#custom-jobs
  class SendEmailJob
    # We need to set the data needed by the job in the initializer
    def initialize(
      to:,
      html_content:,
      locale:,
      subject:,
      account_subdomain:,
      account_id:,
      account_email_sender:,
      support_email:,
      correlation_id: nil
    )
      @to = to
      @html_content = html_content
      @subject = subject
      @locale = locale
      @support_email = support_email
      @account_id = account_id
      @account_subdomain = account_subdomain
      @account_email_sender = account_email_sender
      @correlation_id = correlation_id
    end

    # This will get called by Delayed::Job
    def perform(mailer: EmailCommunicationMailer.new, create_sent_email_job: CreateSentEmailJob)
      mailer_adaptor = LegacyMailerAdaptor.new(
        account_id: @account_id,
        account_email_sender: @account_email_sender,
        support_email: @support_email
      )

      smtp_api_header = {
        unique_args: {
          account_subdomain: @account_subdomain,
          subject: @subject
        }
      }.to_json

      create_sent_email_job.enqueue(
        account_subdomain: @account_subdomain,
        created_at: Time.zone.now.to_time,
        html_content: @html_content,
        sent_to: @to,
        subject: @subject,
        correlation_id: @correlation_id
      )

      mailer.correlation_id = @correlation_id

      mailer.post(
        mailer_adaptor,
        html: @html_content,
        text: nil,
        locale: @locale,
        subject: @subject,
        to: @to,
        "X-SMTPAPI": smtp_api_header
      )
    end

    # We are in the process of simplifying the code paths around sending emails.
    # This adator has the same interface as EmailCommunication, but does not require
    # a conversation or tracks the message sending status on mongo
    class LegacyMailerAdaptor
      attr_writer :status
      attr_reader :account_id, :account_email_sender, :support_email, :id

      def initialize(account_id:, account_email_sender:, support_email:)
        @account_id = account_id
        @account_email_sender = account_email_sender
        @support_email = support_email
        @id = SecureRandom.uuid
      end

      # legacy mailer checks the response as a form of preventing this email to be send twice
      # we are using different methods of achieving this
      def conversation
        nil
      end

      # legacy mailer tracks the status of each sent message on mongo. We
      # do not use that anymore
      def email_events
        LegacyEmailEvents.new
      end

      # legacy mailer tracks the status of each sent message on mongo. We
      # do not use that anymore
      def save!
      end
    end

    class LegacyEmailEvents
      def create(_ops)
      end
    end
  end
end
