module Email
  class CreateSentEmailJob < Jobs::QueueableJob
    attr_reader :account_subdomain, :created_at, :html_content, :sent_to, :subject

    def initialize(
      account_subdomain:,
      created_at:,
      html_content:,
      sent_to:,
      subject:,
      correlation_id: nil,
      logger: Splunk::Logger.new(Rails.logger)
    )
      @account_subdomain = account_subdomain
      @created_at = created_at
      @html_content = html_content
      @sent_to = sent_to
      @subject = subject
      @correlation_id = correlation_id
      @logger = logger
    end

    def perform(create_sent_email: Communications::Commands::CreateSentEmail.new)
      params = {
        account_subdomain: account_subdomain,
        created_at: Time.zone.now.to_time,
        html_content: html_content,
        sent_to: sent_to,
        subject: subject
      }

      create_sent_email.call(params).or do |e|
        Bugsnag.notify(e)
        log(error: e)
      end
    end

    def self.enqueue_options
      {
        priority: 10,
        queue: "uninterruptible_short"
      }
    end

    private

    def log(**attrs)
      @logger.log(
        app: "murmur",
        module: "lib.email.create_sent_email_job",
        correlation_id: @correlation_id,
        **attrs
      )
    end
  end
end
