require "rails_helper"

RSpec.describe Jobs::SurveyLaunch::SurveyInviteJob do
  let(:account) { FactoryBot.create(:account_without_users) }
  let(:people) { Array.new(2) { FactoryBot.create(:person, account: account) } }
  let(:current_user) { FactoryBot.create(:person, account: account) }
  let(:participants) { people[0..1] }
  let(:survey_status) { :active }
  let(:survey_archived) { false }
  let(:survey_event_sourced) { false }
  let(:uninterruptible_queue) { "uninterruptible_short" }
  let(:survey_period_type) { :snapshot }

  let(:survey) do
    FactoryBot.create(
      :basic_survey_with_comms,
      account: account,
      participants: participants,
      status: survey_status,
      archived: survey_archived,
      communicated_close_at: DateTime.now,
      survey_period_type: survey_period_type,
      event_sourced: survey_event_sourced
    )
  end

  let(:expected_start_log_message) do
    {
      event: :batched_survey_launch,
      event_sourced: survey_event_sourced.to_s,
      event_sourced_survey_launch: survey.event_sourced_survey_launch.to_s,
      status: :pending,
      survey_id: job.survey_id.to_s,
      account_subdomain: job.account.subdomain,
      participant_count: participants.count.to_s,
      time_taken: "n/a"
    }
  end

  let(:expected_completion_log_message) do
    {
      event: :batched_survey_launch,
      event_sourced: survey_event_sourced.to_s,
      event_sourced_survey_launch: survey.event_sourced_survey_launch.to_s,
      status: :complete,
      survey_id: job.survey_id.to_s,
      account_subdomain: job.account.subdomain,
      participant_count: participants.count.to_s,
      time_taken: a_string_matching(/\d+(\.\d*)?|\.\d+/)
    }
  end

  let(:s3_client) { Aws::S3::Client.new(stub_responses: true) }
  let(:aws_region) { "Melbourne" }
  let(:bucket_name) { "sent-emails" }

  let(:logger) { instance_double(Splunk::Logger, "Logger") }

  let(:job) { described_class.create!(account: account, survey: survey, type: "invite", status: :hold, executor: current_user) }

  before do
    FactoryBot.create(:templates_survey, :with_snapshot)
    FactoryBot.create(:communication_design, :invite, survey_period: SurveyPeriod.default_snapshot)

    allow(Splunk::Logger).to receive(:new).and_return(logger)
    allow(described_class).to receive(:find).with(job.id).and_return(job)
    allow(logger).to receive(:log)

    FactoryBot.create(:master_survey, name: SecureRandom.uuid, account: account)

    allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
    allow(ENV).to receive(:fetch).and_call_original
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:fetch).with("AWS_REGION").and_return(aws_region)
    allow(ENV).to receive(:fetch).with("SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME").and_return(bucket_name)
  end

  # we can get away with using `all` here as nothing should have been persisted
  # into this collection... yet.
  def persisted_jobs(type)
    Delayed::Job.all.select { |job| job.payload_object.class == type }
  end

  it "runs on an uninterruptible queue" do
    expect(job.queue).to eq uninterruptible_queue
  end

  describe "#run" do
    shared_examples "invites are not sent" do
      it "does not send invites" do
        expect(ActionMailer::Base.deliveries.count).to eq(0)
      end
    end

    context "when survey contains invalid participants" do
      let(:valid_user) { FactoryBot.create(:person, account: account) }
      let(:invalid_user) do
        p = FactoryBot.create(:person, account: account)
        p.name = nil
        p.save(validate: false)
        p
      end
      let(:participants) { [valid_user, invalid_user] }

      it "does not throw an exception" do
        expect { job.run }.to_not raise_error
      end
    end

    context "when launching a survey", delay_jobs: true do
      # speed the spec up by using mock people
      let(:participant_ids) do
        Array.new participant_count, double("Person", id: BSON::ObjectId.new)
      end

      def batch_size
        100
      end

      # how many batch jobs are being created?
      # TODO: refactor this to include the configurable option once merged
      def batch_count
        (participant_count / batch_size) + ((participant_count % batch_size) > 0 ? 1 : 0)
      end

      shared_examples "batch response creation" do
        before do
          # get around the need to create N participants by stubbing out this method
          allow(job).to receive(:participant_ids).and_return(participant_ids)
          job.run
        end

        let(:batch_create_response_jobs) { persisted_jobs Jobs::SurveyLaunch::BatchResponseCreationJob }

        it "persists a scheduling priority value on the job" do
          expect(job.scheduling_priority).to eql expected_priority
        end

        it "enqueues a set of BatchResponseCreationJobs with a weighted priority" do
          expect(batch_create_response_jobs.length).to eql batch_count
          expect(batch_create_response_jobs).to all(have_attributes(priority: expected_priority))
        end

        it "enqueues the set of the BatchResponseCreationJobs on and uninterruptible queue" do
          expect(batch_create_response_jobs.length).to eql batch_count
          expect(batch_create_response_jobs).to all(have_attributes(queue: uninterruptible_queue))
        end
      end

      context "with zero participants" do
        let(:participants) { [] }

        before do
          job.run
        end

        it "job just completes" do
          expect(job.status).to eq :complete
          expect(job.complete).to eq 1.0
          expect(job.completed_at).to_not be_nil
        end
      end

      context "with a small amount of participants" do
        let(:participant_count) { 50 }
        let(:expected_priority) { described_class::BASELINE_SCHEDULING_PRIORITY }

        include_examples "batch response creation"
      end

      context "with a medium amount of participants" do
        let(:participant_count) { 500 }
        let(:expected_priority) { described_class::BASELINE_SCHEDULING_PRIORITY + 1 }

        include_examples "batch response creation"
      end

      context "with a large amount of participants" do
        let(:participant_count) { 5000 }
        let(:expected_priority) { described_class::BASELINE_SCHEDULING_PRIORITY + 2 }

        include_examples "batch response creation"
      end

      context "with an enormous amount of participants" do
        let(:participant_count) { 50_000 }
        let(:expected_priority) { described_class::BASELINE_SCHEDULING_PRIORITY + 3 }

        include_examples "batch response creation"
      end

      shared_examples "batch email sending" do
        let(:responses_reset) { [1, 2, 3] }
        let(:responses_created) { [4, 5, 6] }
        let(:responses_skipped) { [7, 8, 9] }

        before do
          job.run
          job.responses_created(created: responses_created, reset: responses_reset, skipped: responses_skipped)
        end

        let(:batch_email_response_jobs) { persisted_jobs Jobs::SurveyLaunch::BatchEmailSendJob }

        it "enqueues a BatchEmailSendJob with a higher weighted priority" do
          expect(batch_email_response_jobs).to all(have_attributes(priority: job.scheduling_priority - 1))
        end

        it "enqueues a BatchEmailSendJob on and uninterruptible queue" do
          expect(batch_email_response_jobs).to all(have_attributes(queue: uninterruptible_queue))
        end
      end

      context "when receiving message on created responses", delay_jobs: true do
        let(:participant_count) { 50 }

        include_examples "batch email sending"
      end

      context "with the launch survey command stubbed" do
        let(:launch_survey) { instance_double(Surveys::Commands::LaunchSurvey) }

        before do
          allow(Surveys::Commands::LaunchSurvey).to receive(:new).and_return(launch_survey)
        end

        it "launches the survey" do
          expect(launch_survey).to receive(:call).with(
            survey: survey,
            executor_user_id: current_user.aggregate_id,
            account_id: account.aggregate_id,
            correlation_id: "123"
          )
          job.correlation_id = "123"

          job.run
        end

        it "does not fail when the Survey Period has already been launched" do
          allow(launch_survey).to receive(:call)
            .and_raise(EventFramework::Repository::AggregateAlreadyExists)

          job.run
        end
      end
    end

    context "when setting the SURVEY_INVITE_BATCH_SIZE flag", delay_jobs: true do
      before do
        allow(job).to receive(:delay).and_call_original
        survey.assign_config!(Configs::SURVEY_INVITE_BATCH_SIZE, desired_batch_size)
        job.run
      end

      context "when survey invites config set to above participant count" do
        let(:desired_batch_size) { 100 }

        it "generates 1 batch if total participants is under threshhold" do
          expect(persisted_jobs(Jobs::SurveyLaunch::BatchResponseCreationJob).length).to eq 1
        end
      end

      context "when survey invites config set to below participant count" do
        let(:desired_batch_size) { 1 }

        it "generates multiple batches if total participants is over threshhold" do
          expect(persisted_jobs(Jobs::SurveyLaunch::BatchResponseCreationJob).length).to eq 2
        end
      end
    end

    context "when setting the SEND_ENGAGEMENT_EMAILS flag" do
      let(:survey_email_flag) { nil }
      let(:account_email_flag) { nil }
      before do
        survey.assign_config!(Configs::SEND_ENGAGEMENT_EMAILS, survey_email_flag) if survey_email_flag
        account.assign_config!(Configs::SEND_ENGAGEMENT_EMAILS, account_email_flag) if account_email_flag
        allow(job).to receive(:delay).and_call_original
        job.run
      end

      context "if survey config disables email invitation" do
        let(:survey_email_flag) { Configs::DISABLED }

        include_examples "invites are not sent"
      end

      context "if account global config disables email invitation" do
        let(:account_email_flag) { Configs::DISABLED }

        include_examples "invites are not sent"
      end

      context "if survey config enables email invitation" do
        let(:survey_email_flag) { Configs::ENABLED }

        it "sends invites" do
          expect(ActionMailer::Base.deliveries.count).to eq(2)
        end
      end

      context "if account global config enables email invitation" do
        let(:account_email_flag) { Configs::ENABLED }

        it "sends invites" do
          expect(ActionMailer::Base.deliveries.count).to eq(2)
        end
      end
    end

    context "if the survey has not yet been started" do
      let(:survey_status) { :design }
      it "invites all survey participants" do
        job.run

        expect(ActionMailer::Base.deliveries.count).to eq(2)
        expect(survey.responses.count).to eq(2)
        expect(survey.responses.map(&:user)).to match_array(participants)
      end

      context "if the survey has been deleted" do
        before { survey.destroy }
        it "does not throw an exception" do
          expect { job.reload.run }.not_to raise_error
        end
      end

      context "when the survey is hard deleted" do
        before { survey.destroy! }

        it "throws an exception" do
          expect { job.reload.run }.to raise_error(RuntimeError)
        end
      end

      context "when the parent account has been churned" do
        before do
          account.update_attributes status: :churned
          job.run
        end

        include_examples "invites are not sent"

        it "does not create responses" do
          expect(survey.responses.count).to eq(0)
        end

        it "logs an explanation as to why nothing happened" do
          expect(job.result).to eql described_class::CLIENT_CHURNED_MESSAGE
        end

        it "sets status to pending" do
          expect(job).to be_pending
        end
      end

      context "when the survey is not a snapshot one" do
        let(:survey_period_type) { :continuous }

        it "throws an exception" do
          expect { job.reload.run }.to raise_error(RuntimeError)
        end
      end

      # Kiosk users will have no email address but still need responses created
      context "if the user has no email address" do
        let(:kiosk_user) { FactoryBot.create(:user, email: nil, account: account) }
        let(:participants) { people.first(2) + [kiosk_user] }

        before do
          job.run
        end

        it "creates the correct number of responses" do
          expect(survey.responses.count).to eq(3)
        end

        it "creates responses for all users" do
          expect(survey.responses.map(&:user)).to match_array(participants)
        end

        it "only emails users with an email address" do
          expect(ActionMailer::Base.deliveries.count).to eq(2)
        end
      end
    end

    context "if the survey has already started" do
      let(:survey_deleted) { false }
      context "invites are not sent" do
        before do
          survey.destroy if survey_deleted
          job.run
        end

        context "if the survey has been closed since being scheduled" do
          let(:survey_status) { :closed }
          it_behaves_like "invites are not sent"
        end

        context "if the survey has been archived since being scheduled" do
          let(:survey_archived) { true }
          it_behaves_like "invites are not sent"
        end

        context "if the survey has been deleted since being scheduled" do
          let(:survey_deleted) { true }
          it_behaves_like "invites are not sent"
        end
      end

      context "when people have already been invited" do
        let(:newbie) { FactoryBot.create(:user, account: account) }

        before do
          participants.each { |p| Response.create!(survey: survey, user: p) }
          survey.add_participant!(newbie)
          job.run
        end

        it "should invite any new participants only" do
          expect(ActionMailer::Base.deliveries.count).to eq(1)
          expect(survey.responses.count).to eq(3)
          expect(survey.responses.map(&:user)).to match_array(participants + [newbie])
        end
      end

      context "if the response has been deleted" do
        let(:user) { FactoryBot.create(:user, account: account) }
        let(:response) { Response.create!(survey: survey, user: user) }
        let(:participants) { [] }

        before do
          # creates a deleted response
          response.status = :deleted
          response.save!

          survey.add_participant!(user)
          job.run
        end

        it "revives the response" do
          expect(response.reload.status).to eq :active
        end

        it "invites the participant of the revived response" do
          expect(survey.responses.where(user: user).count).to eq 1
          expect(ActionMailer::Base.deliveries.count).to eq(1)
        end
      end
    end

    context "when a user has a demo response" do
      let!(:user) { FactoryBot.create(:person, account: account) }
      let!(:response) { FactoryBot.create(:response, survey: survey, user: user, demonstration: true) }
      let(:participants) { [user] }

      it "sends an invitations" do
        expect(Email::SendEmailJob).to receive(:new)
          .with(hash_including(to: user.email))
          .and_call_original

        job.run

        expect(ActionMailer::Base.deliveries.count).to eq(1)
        expect(job.participants).to include user
      end

      it "resets the response" do
        job.run
        expect(response.reload.demonstration?).to be false
      end
    end

    context "before the job runs" do
      it "is not completed" do
        expect(job).to_not be_completed
      end

      it "has undefined completion date" do
        expect(job.completed_at).to be_nil
      end

      it "is not running yet" do
        expect(job.status).to eq(:hold)
      end

      it "has no pending children jobs" do
        expect(job.job_stats).to be_empty
      end
    end

    context "while the job runs, and child delayed jobs did not run", delay_jobs: true do
      before "log that the job is running" do
        expect(described_class::LOGGER).to receive(:log).with(hash_including(expected_start_log_message))
        job.run
      end

      it "is not completed" do
        expect(job).to_not be_completed
      end

      it "has zero complete progress" do
        expect(job.complete).to eq 0
      end

      it "has undefined completion date" do
        expect(job.completed_at).to be_nil
      end

      it "is set as pending" do
        expect(job.status).to eq(:pending)
      end

      it "has pending children jobs" do
        expect(job.job_stats).to eq(uninterruptible_short: 1)
      end
    end

    context "after the job runs" do
      before "log that job is running and when it finishes" do
        allow(described_class::LOGGER).to receive(:log)
        job.run
        expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_start_log_message))
        expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_completion_log_message))
      end

      it "is set as completed" do
        expect(job).to be_completed
        expect(job.status).to eq(:complete)
      end

      it "has full complete progress" do
        expect(job.complete).to eq 1
      end

      it "has defined completion date" do
        expect(job.completed_at).to be_present
      end

      it "has no pending children jobs" do
        expect(job.job_stats).to be_empty
      end

      context "one user does not have email" do
        let(:person_without_email) { FactoryBot.create(:person, account: account, email: nil, employee_id: 1) }
        let(:participants) { people[0..1] + [person_without_email] }

        it "has full complete progress" do
          expect(job.complete).to eq 1
        end
      end
    end

    context "when logging if the survey is event sourced" do
      context "when survey is event sourced" do
        let(:survey_event_sourced) { true }

        before "log that job is running and when it finishes" do
          allow(described_class::LOGGER).to receive(:log)
          job.run
          expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_start_log_message))
          expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_completion_log_message))
        end

        it "it manages to complete" do
          expect(job).to be_completed
          expect(job.status).to eq(:complete)
        end
      end

      context "when survey is not event sourced" do
        let(:survey_event_sourced) { false }

        before "log that job is running and when it finishes" do
          allow(described_class::LOGGER).to receive(:log)
          job.run
          expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_start_log_message))
          expect(described_class::LOGGER).to have_received(:log).with(hash_including(expected_completion_log_message))
        end

        it "it manages to complete" do
          expect(job).to be_completed
          expect(job.status).to eq(:complete)
        end
      end
    end

    context "when response batches are created" do
      context "when there are multiple batches" do
        before do
          survey.assign_config!(Configs::SURVEY_INVITE_BATCH_SIZE, 1)
          job.run
        end

        it "persists multiple response batches" do
          expect(job.complete).to eq(1.0)
          expect(job.status).to eq(:complete)
          expect(job.completed_participants_count).to eq(2)
        end
      end
      context "when there is a single response batch" do
        before do
          survey.assign_config!(Configs::SURVEY_INVITE_BATCH_SIZE, 2)
          job.run
        end

        it "updates the completeness statuses" do
          expect(job.complete).to eq(1.0)
          expect(job.status).to eq(:complete)
          expect(job.completed_participants_count).to eq(2)
        end
      end
    end
  end

  describe "#set_logger" do
    let(:logger_2) { instance_double(Splunk::Logger, "Logger") }

    before do
      allow(logger_2).to receive(:log)
    end

    it "sends any log messages via the alternate logger" do
      job.set_logger(logger_2)
      job.run
      expect(logger_2).to have_received(:log).with(hash_including(expected_start_log_message))
      expect(logger_2).to have_received(:log).with(hash_including(expected_completion_log_message))
    end
  end

  describe "#number_of_batches" do
    let(:people) { Array.new(11) { FactoryBot.create(:person, account: account) } }
    let(:participants) { people[0..11] }

    before do
      survey.assign_config!(Configs::SURVEY_INVITE_BATCH_SIZE, 10)
      job.run
    end

    it "calculates the number of batches" do
      expect(job.number_of_batches).to be(2)
    end
  end

  describe "#responses_created" do
    context "when there are no invites to be sent" do
      it "does not enqueue any email jobs" do
        expect(Jobs::SurveyLaunch::BatchEmailSendJob).not_to receive(:prioritize)

        job.responses_created(created: [], reset: [], skipped: [])
      end
    end
  end
end
