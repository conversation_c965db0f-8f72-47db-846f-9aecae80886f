require "rails_helper"
require "timecop"

RSpec.describe Email::SendEmailJob do
  let(:account_subdomain) { "emailtest" }
  let(:html_content) { "<h1>hello</h1>" }
  let(:sent_to) { "<EMAIL>" }
  let(:subject) { "hello there" }
  let(:support_email) { "<EMAIL>" }
  let(:locale) { "en" }
  let(:account_email_sender) { "mckenzie" }
  let(:account) do
    Account.create!(
      name: "email test",
      subdomain: account_subdomain,
      region: "APAC"
    )
  end
  let(:content) do
    {
      account_subdomain: account_subdomain,
      created_at: Time.utc(2020),
      html_content: html_content,
      sent_to: sent_to,
      subject: subject
    }
  end
  let(:s3_client) { Aws::S3::Client.new(stub_responses: true) }
  let(:region_name) { "Melbourne" }
  let(:bucket_name) { "sent-emails" }

  before do
    allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
  end

  it "sends an email" do
    ENV["AWS_REGION"] = region_name
    ENV["SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME"] = bucket_name

    job = Email::SendEmailJob.new(
      to: sent_to,
      html_content: html_content,
      locale: locale,
      subject: subject,
      account_subdomain: account.subdomain,
      account_id: account.id,
      account_email_sender: account_email_sender,
      support_email: support_email
    )

    job.perform

    expect(ActionMailer::Base.deliveries.count).to eq(1)
    expect(ActionMailer::Base.deliveries.first.subject).to eq("hello there")
  end

  context "records the sent email" do
    let(:s3_object) { instance_double(Aws::S3::ObjectSummary) }
    let(:object_list) { [s3_object] }
    let(:s3_bucket) { instance_double(Aws::S3::Bucket, objects: object_list) }

    before do
      job = Email::SendEmailJob.new(
        to: sent_to,
        html_content: html_content,
        locale: locale,
        subject: subject,
        account_subdomain: account.subdomain,
        account_id: account.id,
        account_email_sender: account_email_sender,
        support_email: support_email
      )

      job.perform

      allow(Communications::Commands::BuildS3BucketObject).to receive_message_chain(:new, :call).and_return(s3_bucket)
      allow(s3_object).to receive_message_chain(:get, :body, :read).and_return(content.to_json)
    end

    it "when there is one email" do
      sent_emails = Communications::Queries::GetSentEmails.new.call(sent_to: "<EMAIL>")
      collection = sent_emails.value!
      expect(collection.first.subject).to eq(subject)
      expect(collection.first.html_content).to eq(html_content)
      expect(collection.first.sent_to).to eq(sent_to)
      expect(collection.first.account_subdomain).to eq(account_subdomain)
    end
  end

  it "creates the CreateSentEmailJob with the right params" do
    job = Email::SendEmailJob.new(
      to: sent_to,
      html_content: html_content,
      locale: locale,
      subject: subject,
      account_subdomain: account.subdomain,
      account_id: account.id,
      account_email_sender: account_email_sender,
      support_email: support_email
    )
    Timecop.freeze

    create_sent_email_job = class_double(Email::CreateSentEmailJob)
    expect(create_sent_email_job).to receive(:enqueue).with(
      account_subdomain: account.subdomain,
      created_at: Time.zone.now.to_time,
      html_content: html_content,
      sent_to: sent_to,
      subject: subject,
      correlation_id: nil
    )

    job.perform(create_sent_email_job: create_sent_email_job)
    Timecop.return
  end
end
