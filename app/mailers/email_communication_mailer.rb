# Provides a basic interface for EmailCommunications
# to use the ActionMailer system. You could use Mail:: directly,
# but ActionMailer already solves a bunch of problems for you.
class EmailCommunicationMailer < ActionMailer::Base
  attr_accessor :correlation_id

  MANDATORY_KEYS = [:to, :subject]

  # Necessary because <PERSON><PERSON><PERSON><PERSON><PERSON> is wiggity wack. We want to access
  # the actual mail response, but <PERSON> hides that in Delayed::PerformableMailer.
  # Boo. We deliver ourselves and then return a stub to <PERSON>. There might
  # be a better way to do this; perhaps Rails 4 & their background jobs API
  # will provide a way.
  class DummyMail
    def deliver
    end
  end

  # Sends an email with the given text and html bodies. At least
  # one must be supplied.
  #
  # The options hash is a standard Mail hash. It must minimally
  # include the :to and :subject values.
  #
  # It must also include one or both of :text & :html for the content.
  # Note that an empty text or html value is ok, but not nil.
  #
  # @param [ EmailCommunication ] email The email communication being sent
  # @param [ Hash ] opts The email options
  # @return [ Message ] the delivered email
  def post(email, opts)
    # This check is a hack to ensure an email is not sent for a response that's
    # already submitted, as can happen when a future dated run_date is provided.
    associated_response = email.conversation&.response
    if submitted_non_lifecycle_response?(associated_response)
      log(
        correlation_id: correlation_id,
        message: "Skipping email to email:#{opts[:to]} as the response it relates to has already been submitted. See EmailCommunication:#{email.id}"
      )
      return
    end

    dispatcher = Services::EmailDispatcher.new(email)
    mailer_locale = opts.delete(:locale).presence || I18n.locale

    I18n.with_locale(mailer_locale) do
      # NOTE: `opts` are mutated inside `generate_mail_to_deliver`
      mutating_opts = opts.clone

      mail_to_deliver = generate_mail_to_deliver(
        email,
        dispatcher.from_address,
        mutating_opts,
        associated_response
      )
      dispatcher.deliver(mail_to_deliver, mutating_opts)

      @_message = DummyMail.new # Nuke the double send with delayed job
      mail_to_deliver
    end
  rescue => e
    log(error: e, correlation_id: correlation_id)
    raise e
  end

  def generate_mail_to_deliver(email, from, opts, response = nil)
    # NOTE: `opts` are mutated here!

    # Assign the instance variable, so that it is accessible in a template.
    @email = email
    @survey_type = survey_type(response)

    # Set the default option values
    opts[:from] ||= from
    opts[:reply_to] ||= email.support_email

    # The content
    @text = opts.delete(:text)
    @html = opts.delete(:html)

    # Mandatory Keys
    missing = [:to, :subject].reject { |v| opts.key?(v) }
    missing += [:text, :html] if @text.nil? && @html.nil?
    fail "The '#{missing.join(", ")}' values must be set to send an email" unless missing.empty?

    # Generate the email
    log(
      correlation_id: correlation_id,
      message: "Generating email to email:#{opts[:to]} with subject:'#{opts[:subject]}'. See EmailCommunication:#{email.id}"
    )
    mail({template_name: "post"}.merge(opts))
  rescue => e
    log(correlation_id: correlation_id, error: e)
    raise e
  end

  private

  def log(**attrs)
    Splunk::Logger.new(Rails.logger).log(
      app: "murmur",
      module: "app.mailers.email_communication_mailer",
      **attrs
    )
  end

  def submitted_non_lifecycle_response?(response)
    return false unless response
    return false if response.survey.lifecycle?

    response.status == :submitted
  end

  def survey_type(response)
    return :not_available unless response

    response.survey.type
  end
end
