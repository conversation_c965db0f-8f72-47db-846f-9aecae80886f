require_relative "../conversation"

#
# States:
# * new - conversation created, but no action has been taken
# * invited
# * completed
# * reminder
class Conversation
  class Engagement < Conversation
    include CommunicationHelper
    include ResponseHelper

    def trigger_final(opts)
      deliver_template("final", opts)
      self.status = :final
      save!
    end

    def trigger_invite(opts)
      deliver_template("invite", opts)

      self.status = :invited
      save!
    end

    def trigger_reminder(opts)
      deliver_template("reminder", opts)
      self.status = :reminder
      save!
    end

    def trigger_resend(_opts)
      template_name = [:invited, :new].include?(status) ? "invite" : status.to_s
      deliver_template(template_name)
      save!
    end

    protected

    def account
      @account ||= survey.account
    end

    def recipient
      @recipient ||= response.user
    end

    def survey
      @survey ||= response.survey
    end

    def config
      @config ||= Conversation::Config::Engagement.new(survey)
    end

    private

    def default_locale
      locales = survey.supported_locales
      recipient.select_preferred_locale(locales)
    end

    def deliver_template(template_name, opts = {})
      enqueue_options = {
        priority: opts[:priority],
        max_attempts: opts[:max_attempts],
        causation_id: opts[:causation_id],
        run_at: opts[:run_at] || Time.now.utc
      }

      if survey.send_engagement_emails? && recipient.emailable?
        Communications::Email::SendEmail.new(correlation_id: opts[:correlation_id]).call(
          account: survey.account,
          survey: survey,
          response: response,
          recipient: recipient,
          template_type: template_name,
          locale: default_locale,
          enqueue_options: enqueue_options
        )
      end

      if survey.slack_ready? && recipient.slackable?
        deliver_to_slack(
          template_name: template_name,
          enqueue_options: enqueue_options
        )
      end

      send_msteams_notification(template_name: template_name, enqueue_options: enqueue_options) if survey.send_engagement_ms_teams_messages?
      send_sms(template_name: template_name, enqueue_options: enqueue_options) if Surveys::Queries::CanSendSms.new.call(survey_aggregate_id: survey.aggregate_id).value_or(false)

      if template_name == "invite"
        begin
          # If one tracking call fails, they're all going to fail, so unlikely to be issues around ordering
          log_amplitude_invite_sent(Analytics::MEDIUM_SLACK) if survey.slack_ready? && recipient.slackable?
          log_amplitude_invite_sent(Analytics::MEDIUM_TEAMS) if survey.send_engagement_ms_teams_messages?
        rescue
          nil
        end
      end
    end

    def deliver_to_slack(template_name:, enqueue_options:)
      content_variables = Communications::Slack::EngagementCommunicationVariables.new.call(
        survey: survey,
        response: response,
        locale: default_locale
      )

      message_content = Communications::Slack::CommunicationContent.new.call(
        survey_id: survey.id,
        survey_type: survey.type,
        template_type: template_name,
        locale: default_locale
      )

      Communications::Slack::SendDirectMessage.new.call(
        account: recipient.account,
        employee: recipient,
        text: content_variables.call(content: message_content),
        enqueue_options: enqueue_options
      )
    rescue KeyError => e
      # This captures cases where the template has broken variables that do not exist in content_variables
      Bugsnag.notify(e)
    end

    def send_msteams_notification(template_name:, enqueue_options:)
      field_values = Communications::Msteams::EngagementCommunicationVariables.new.call(
        survey: survey,
        response: response,
        locale: default_locale
      )

      MicrosoftTeams::TeamsNotifier.new.call(
        person: recipient,
        account: account,
        message: I18n.t("msteams.#{survey.type}.#{template_name}", locale: default_locale) % field_values,
        enqueue_options: enqueue_options
      )
    end

    def send_sms(template_name:, enqueue_options:)
      Delayed::Job.enqueue(
        Communications::Jobs::SendCommunicationViaSms.new(
          response_aggregate_id: response.aggregate_id,
          template_type: template_name,
          locale: default_locale
        ),
        enqueue_options
      )
    end

    private

    def log_amplitude_invite_sent(medium)
      Analytics.log_event(
        user_id: response.user.aggregate_id,
        event_type: "Platform Communication Received",
        event_properties: {
          Analytics::EVENT_PROPERTY_MEDIUM => medium,
          Analytics::EVENT_PROPERTY_COMMUNICATION_NAME => Analytics::COMMUNICATION_NAME_SURVEY_CAPTURE
        },
        group_properties: [Analytics::ACCOUNT_GROUP.call(account.aggregate_id), Analytics::SURVEY_GROUP.call(survey.aggregate_id)]
      )
    end
  end
end
