import * as React from 'react';
import styles from './SummaryInsightReportApp.scss';
import { connect } from 'react-redux';
import { getReportDataState } from 'ca-reports/reducers/ReportsState';
import { getHeaderState } from 'ca-header/state/HeaderState';
import getParamsForAppliedState from 'ca-reports/state/getParamsForAppliedState';
import { gotoSinglePageAppPage } from 'ca-reports/actions/SinglePageAppActions';
import { ContextualReportsTitleBlock } from 'ca-reports/ContextualReportsTitleBlock';
import { getExportActionIconById } from 'ca-header/utils';
import ErrorBoundary from 'ca-utils/ErrorBoundary';
import InsufficientResults from 'ca-reports/components/InsufficientResults/InsufficientResults';
// @ts-expect-error
import QueryStatus from './QueryStatus';
// @ts-expect-error
import SummaryReportHeroBannerBackground from './SummaryReportHeroBannerBackground';
import { ReportsAppContext } from 'ca-reports/ReportsAppContext';
import { InlineNotification } from '@kaizen/notification';
import SupportEmail from 'ca-action-framework/components/SupportEmail/SupportEmail';
// @ts-expect-error
import FactorHero from './FactorHero';
import CommentaryBlock from './CommentaryBlock/CommentaryBlock';
import { INSIGHT, QUESTIONS } from 'ca-reports/ReportPageTypeConstants';
import SummaryReportHeroBanner from './SummaryReportHeroBanner/SummaryReportHeroBanner';
import StrengthAndOpportunities from './components/StrengthAndOpportunities/StrengthAndOpportunities';
import CallToAction from './components/CallToAction/CallToAction';
import Carousel from './components/Carousel/Carousel';
import Comparisons from './components/Comparisons/Comparisons';
import IndexFactorScoreDetails from './components/IndexFactorScoreDetails/IndexFactorScoreDetails';
import { calculatePercentage } from './utils';
import { Text, MenuItemProps } from '@kaizen/components';
import { Icon } from '@kaizen/components/future';
import { ConnectSelectLocale } from '../ca-locale-switcher/SelectLocale';
import getExportActions from 'ca-report-export/helpers/getExportActions';
import ReportExports from 'ca-report-export/components/ReportExports';
import type { ReportExportLink, RootState } from 'ca-reports/types';
import setParamsInUrl from 'ca-navigation/setParamsInUrl';
import { isPdfPath, setParamsInPdfTargetPath } from 'ca-utils/pdfPath';
import classNames from 'classnames';
import type { Locale } from 'ca-locale-switcher/LocaleTypes';
import withLocale from 'ca-locale-switcher/withLocale';
import { withPrint, PrintProps } from 'ca-reports/withPrint';
import { ReportViewedEventTracker } from '../ca-reports/ReportTrackers';
import FilterPrivacyProtectionAcademy from 'ca-reports/components/InsufficientResults/FilterPrivacyProtectionAcademy';
import WithReportPageTitle from 'ca-reports/WithReportPageTitle';
import { ImmutableType } from 'ca-reports/immutable.types';
import {
  ReportHeader,
  Reports,
  SummaryInsightReportData,
  SummaryInsightReportDataUiText,
  SurveyStatus,
} from 'ca-reports/bigBallOfProps';
import { type IntlProps, withReactIntl } from 'ca-reports/withReactIntl';
import {
  Comparison,
  FactorScore,
  HighestQuestion,
  Participation,
  RecommendedQuestion,
  SummaryViewType,
  ToggleBarText,
  TreasureDataType,
} from './types';
import { type Params } from 'ca-navigation/setParamsInCurrentUrl';
import { type ReportExportAction } from 'ca-reports/typescriptTypes';
import { ContextType } from 'react';

type ComponentProps = {
  reportData: ImmutableType<SummaryInsightReportData>;
  queryStatus: {
    queryStatus: string;
    emailSupport: string;
  };
  reportsInitialData: ImmutableType<Reports<SummaryInsightReportData>>;
  header: ImmutableType<ReportHeader>;
  locale: Locale;
  toggleBarText: ToggleBarText;
};

type DispatchProps = {
  gotoSinglePageAppPage: any;
};

type ReduxProps = {
  headerContent: ReportHeader;
  linkParams: Params;
};

type Props = ComponentProps &
  DispatchProps &
  ReduxProps &
  IntlProps &
  PrintProps;

type SummaryInsightReportDataType = {
  actionFramework: {
    focusAreaCount: number;
    actionCount: number;
    actionCompletedCount: number;
  };
  comparisons: Array<Comparison>;
  highestQuestions: Array<HighestQuestion>;
  recommendedQuestions: Array<RecommendedQuestion>;
  participation: Participation;
  isAllResults: boolean;
  indexFactorName: string;
  indexFactorScore: FactorScore;
  questionsReportPath: string;
  takeActionReportPath: string;
  createActionPath: string;
  actionFrameworkApiRootPath: string;
  treasureData: TreasureDataType;
  summaryView?: SummaryViewType | null;
  actionFrameworkEnabled: boolean;
  reportOwner: boolean;
  selectFocusAreaCreateActionEnabled: boolean;
  commentary: string | null;
};

type State = {
  summaryView: SummaryViewType;
  uiText: SummaryInsightReportDataUiText;
};

type Report = {
  name: string;
  url: string;
  current: boolean;
};

type ReactSelectOption = {
  value: string;
  label: string;
};

const transformReportToReactSelectOption: (r: Report) => ReactSelectOption = (
  r
) => ({
  value: r.url,
  label: r.name,
});

const transformReportsToReactSelectOptions: (
  reports: Array<Report>
) => Array<ReactSelectOption> = (reports) =>
  reports.map(transformReportToReactSelectOption);

const getCurrentReportAsReactSelectOption: (
  reports: Array<Report>
) => ReactSelectOption | undefined = (reports) => {
  const currentReport = reports && reports.find((r) => r.current);
  if (currentReport) return transformReportToReactSelectOption(currentReport);
  return undefined;
};

const handleReportSwitcherChange: (
  selectedOption: ReactSelectOption
) => void = (selectedOption) => {
  if (selectedOption) {
    window.open(selectedOption.value, '_self');
  }
};

const getReportSwitcherProps = (
  selectLabel: string,
  reports?: Array<Report>
) => {
  if (!reports || reports.length === 0) return undefined;

  const transformedReports = transformReportsToReactSelectOptions(reports);
  const currentReport = getCurrentReportAsReactSelectOption(reports);

  if (transformedReports.length === 1 && currentReport)
    return {
      subtitle: currentReport.label,
    };

  return {
    pageSwitcherSelect: {
      onChange: handleReportSwitcherChange,
      options: transformedReports,
      value: currentReport,
      isSearchable: false,
      label: selectLabel,
    },
  };
};

export class SummaryInsightReportApp extends React.Component<Props, State> {
  static contextType = ReportsAppContext;
  declare context: ContextType<typeof ReportsAppContext>;

  state = {
    summaryView: this.getSummaryInsightReportData().summaryView || 'comparison',
    uiText: this.props.reportData.get('uiText').toJS(),
  };

  getSummaryInsightReportData():
    | SummaryInsightReportDataType
    | Record<string, never> {
    if (!this.props.reportData.get('summaryInsightReportData')) return {};

    const summaryInsightReportData = this.props.reportData
      .get('summaryInsightReportData')
      .toJS();

    return {
      ...summaryInsightReportData,
      comparisons: summaryInsightReportData.comparisons.map((comparison) => ({
        ...comparison,
        indexFactorScore: calculatePercentage(comparison.indexFactorScore),
      })),
    };
  }

  getCarouselViews(
    summaryInsightReportData:
      | SummaryInsightReportDataType
      | Record<string, never>,
    uiText: SummaryInsightReportDataUiText
  ) {
    const { indexFactorScoreDetails, comparison } = uiText;
    return [
      {
        name: comparison.name,
        type: 'comparison' as SummaryViewType,
        component: (
          <Comparisons
            comparisons={summaryInsightReportData.comparisons}
            indexFactorScore={summaryInsightReportData.indexFactorScore}
            uiText={comparison}
          />
        ),
      },
      {
        name: indexFactorScoreDetails.name,
        type: 'detail' as SummaryViewType,
        component: (
          <IndexFactorScoreDetails
            indexFactorScore={summaryInsightReportData.indexFactorScore}
            indexFactorName={summaryInsightReportData.indexFactorName}
            participation={summaryInsightReportData.participation}
            uiText={indexFactorScoreDetails}
          />
        ),
      },
    ];
  }

  updateView = (view: SummaryViewType) => {
    this.setState((prevState) => ({ ...prevState, summaryView: view }));
  };

  updateActionLinksList(
    actionLinksList: Array<ReportExportLink>
  ): Array<ReportExportLink> {
    const paramsToSet = {
      summaryView: this.state.summaryView,
    };
    return actionLinksList
      .filter(
        (actionLink) =>
          actionLink.type === 'print' || actionLink.type === 'export'
      )
      .map((actionLink) => ({
        ...actionLink,
        url: isPdfPath(actionLink.url)
          ? setParamsInPdfTargetPath(actionLink.url, paramsToSet)
          : setParamsInUrl(actionLink.url, paramsToSet),
      }));
  }

  renderZenExportActions(reportExportProps: any) {
    const actionLinks = this.props.reportData.get('actionLinks').toJS();
    if (actionLinks) {
      const exportActions = getExportActions(
        this.updateActionLinksList(actionLinks.actionLinksList),
        reportExportProps.onExportStart,
        this.props.print
      );
      if (exportActions) {
        return [
          {
            label: actionLinks.uiText.exportLabel,
            menuItems: exportActions.map(
              (exportAction: ReportExportAction): MenuItemProps => {
                const { href, onClick } = exportAction;
                return {
                  label: exportAction.title,
                  icon: getExportActionIconById(exportAction.id),
                  href,
                  onClick,
                };
              }
            ),
          },
        ];
      }
      return [];
    }
    return [];
  }

  renderZenConfigLink(configure: string) {
    const configLink = this.props.reportData.getIn([
      'header',
      'configure',
      'path',
    ]);

    if (configLink === null || configLink === undefined) {
      return [];
    }
    return [
      {
        label: configure,
        icon: <Icon isFilled isPresentational name="build" />,
        href: configLink,
      },
    ];
  }

  getSurveyName() {
    return this.props.reportData.getIn(['header', 'surveyName']);
  }

  renderZenStatusBadge = (
    status: SurveyStatus
  ): { text: string; status: 'live' | 'draft' } | undefined => {
    if (status === 'active') return { text: 'Live', status: 'live' };
    if (status === 'design') return { text: 'Draft', status: 'draft' };
    return undefined;
  };

  renderGlobalIATitleBlock() {
    const {
      intl: { formatMessage },
    } = this.props;
    const { uiText } = this.state;
    const isAdminReport =
      this.props.reportData.getIn(['header', 'reports', 'current']) ===
      'Administrator Report';

    const reports = this.getReports();
    const selectLabel = formatMessage({
      id: 'reports.titles.select',
      defaultMessage: 'Select report',
    });

    return (
      <div className={styles.titleBlockContainer}>
        <header className={styles.titleBlock}>
          {this.props.headerContent && (
            <ReportExports>
              {(reportExportProps) => (
                <ContextualReportsTitleBlock
                  title={`${uiText.titleBlock.title}: ${this.getSurveyName()}`}
                  breadcrumb={{
                    path: this.props.reportsInitialData.getIn([
                      'routeTemplates',
                      'reportsIndex',
                    ]),
                    text: uiText.titleBlock.back,
                  }}
                  surveyStatus={this.renderZenStatusBadge(
                    this.props.headerContent.surveyStatus
                  )}
                  {...getReportSwitcherProps(selectLabel, reports)}
                  defaultAction={
                    isAdminReport
                      ? {
                          label: uiText.titleBlock.share,
                          href: this.props.headerContent.share.path,
                          reversed: true,
                        }
                      : undefined
                  }
                  secondaryActions={[
                    ...this.renderZenConfigLink(uiText.titleBlock.configure),
                    ...this.renderZenExportActions(reportExportProps),
                  ]}
                  // @ts-expect-error
                  links={this.props.headerContent.links}
                  hideActionFrameworkLink={true}
                  hideParticipationLink={
                    // @ts-expect-error
                    this.props.headerContent.hideParticipationLink
                  }
                  // @ts-expect-error
                  activeLinkType={this.props.headerContent.activeLink}
                  overwriteRedirection={true}
                  linkParams={this.props.linkParams}
                  onCustomNavigation={this.context.onCustomNavigation}
                  shouldUseCustomNavigation={
                    this.context.shouldUseCustomNavigation
                  }
                  gotoSinglePageAppPage={this.props.gotoSinglePageAppPage}
                  summaryInsightReportEnabled={true}
                />
              )}
            </ReportExports>
          )}
        </header>
      </div>
    );
  }

  getReports() {
    const reports = this.props.reportData.getIn([
      'header',
      'reports',
      'available',
    ]);

    if (reports === null || reports === undefined) {
      return undefined;
    }

    return reports.toJS();
  }

  getHomePath() {
    return this.props.reportData.getIn(['header', 'account', 'homePath']);
  }

  isInsignificant() {
    const insignificantFilters = this.props.reportData.get(
      'insignificantFilters'
    );
    const responseCount = this.props.reportData.getIn([
      'participationSummary',
      'responseCount',
    ]);

    return insignificantFilters || (responseCount && responseCount === -1);
  }

  renderConfidentialityNotification = () => {
    const {
      uiText: {
        factorPrivacyNotification: { title, content, academyLinkText },
      },
    } = this.state;
    return (
      <div className={styles.notification}>
        <InlineNotification
          type="cautionary"
          title={title}
          automationId="factorPrivacyNotification"
        >
          {content}
          <br />
          <br />
          <FilterPrivacyProtectionAcademy
            supportArticleLink="https://support.cultureamp.com/hc/en-us/articles/*********#h_d88f8bff-7e6c-4798-a25a-7a11ca76a6e2"
            supportArticleLinkCopy={academyLinkText}
          />
        </InlineNotification>
      </div>
    );
  };

  renderHeroBannerContent() {
    const { locale } = this.props;

    const summaryInsightReportData = this.getSummaryInsightReportData();

    const {
      indexFactorName,
      indexFactorScore,
      participation,
      comparisons,
      summaryView,
    } = summaryInsightReportData;

    const { uiText } = this.state;

    return (
      <SummaryReportHeroBanner>
        <div className={styles.heroBannerContainer}>
          <div className={styles.factorHero}>
            <FactorHero
              indexFactorName={indexFactorName}
              indexFactorScore={indexFactorScore}
              uiText={uiText.hero}
            />
          </div>
          <div className={styles.details}>
            {comparisons && comparisons.length > 0 ? (
              <Carousel
                locale={locale}
                views={this.getCarouselViews(summaryInsightReportData, uiText)}
                onUpdatedView={this.updateView}
                defaultView={summaryView}
              />
            ) : (
              <IndexFactorScoreDetails
                indexFactorScore={indexFactorScore}
                indexFactorName={indexFactorName}
                participation={participation}
                uiText={uiText.indexFactorScoreDetails}
              />
            )}
          </div>
        </div>
      </SummaryReportHeroBanner>
    );
  }

  renderFactorScores = () => {
    return (
      <SummaryReportHeroBannerBackground>
        {this.renderGlobalIATitleBlock()}
        {this.renderHeroBannerContent()}
      </SummaryReportHeroBannerBackground>
    );
  };

  renderPrivacyNotification = () => {
    return (
      <>
        <SummaryReportHeroBannerBackground>
          {this.renderGlobalIATitleBlock()}
        </SummaryReportHeroBannerBackground>
        {this.renderConfidentialityNotification()}
      </>
    );
  };

  renderSummaryReportFactorScoresOrPrivacyNotification = () => {
    const { indexFactorScore } = this.getSummaryInsightReportData();
    return indexFactorScore && indexFactorScore.significant
      ? this.renderFactorScores()
      : this.renderPrivacyNotification();
  };

  renderErrorNotification() {
    const { error } = this.state.uiText.bannerText;
    return (
      <div className={styles.error}>
        <InlineNotification
          type="negative"
          title={error.requestFailedTitle}
          persistent
        >
          <div>
            <SupportEmail
              bodyText={error.supportEmailText}
              textToReplace="%{email_support_link}"
            />
          </div>
        </InlineNotification>
      </div>
    );
  }

  renderCommentary() {
    const summaryInsightReportData = this.getSummaryInsightReportData();
    const { commentary } = summaryInsightReportData;

    if (commentary === null) {
      return null;
    }

    return <CommentaryBlock commentary={commentary} />;
  }

  renderCallToAction() {
    const summaryInsightReportData = this.getSummaryInsightReportData();
    const { actionFrameworkEnabled, selectFocusAreaCreateActionEnabled } =
      summaryInsightReportData;
    const { reportTypes } = this.props.reportData.toJS();
    if (
      actionFrameworkEnabled === false ||
      !reportTypes.includes('insight_report')
    ) {
      return null;
    }

    return (
      <ErrorBoundary fallbackComponent={this.renderErrorNotification()}>
        <CallToAction
          recommendedQuestions={summaryInsightReportData.recommendedQuestions}
          actionFrameworkCount={summaryInsightReportData.actionFramework}
          showComparison={!summaryInsightReportData.isAllResults}
          questionsReportPath={summaryInsightReportData.questionsReportPath}
          takeActionReportPath={summaryInsightReportData.takeActionReportPath}
          createActionPath={summaryInsightReportData.createActionPath}
          actionFrameworkApiRootPath={
            summaryInsightReportData.actionFrameworkApiRootPath
          }
          bannerText={this.state.uiText.bannerText}
          treasureData={summaryInsightReportData.treasureData}
          navigateToQuestionReport={() =>
            this.context.onCustomNavigation(QUESTIONS, {
              overwrite_redirection: true,
            })
          }
          locale={this.props.locale}
          selectFocusAreaCreateActionEnabled={
            selectFocusAreaCreateActionEnabled
          }
        />
      </ErrorBoundary>
    );
  }

  renderStrengthsAndOpportunities() {
    const summaryInsightReportData = this.getSummaryInsightReportData();
    return (
      <StrengthAndOpportunities
        showComparison={!summaryInsightReportData.isAllResults}
        strength={summaryInsightReportData.highestQuestions}
        opportunities={summaryInsightReportData.recommendedQuestions}
        isReportOwner={summaryInsightReportData.reportOwner}
        uiText={{
          questions: this.state.uiText.questionsText,
          comparison: this.state.uiText.comparison,
        }}
      />
    );
  }

  renderFooter() {
    const { uiText } = this.state;
    const { reportTypes } = this.props.reportData.toJS();

    if (!reportTypes.includes('insight_report')) {
      return null;
    }
    return (
      <div className={styles.footer}>
        <div className={styles.leftContainer}></div>
        <div className={styles.centerContainer}>
          <Text tag="div" variant="small">
            {uiText.footer.footerText}{' '}
            <a
              onClick={() =>
                this.context.onCustomNavigation(INSIGHT, {
                  overwrite_redirection: true,
                })
              }
              tabIndex={0}
            >
              {uiText.footer.footerLink}
            </a>
          </Text>
        </div>
        <div className={styles.rightContainer}>
          <ConnectSelectLocale />
        </div>
      </div>
    );
  }

  render() {
    const {
      queryStatus,
      locale,
      intl: { formatMessage },
    } = this.props;
    return (
      <WithReportPageTitle
        title={formatMessage({
          id: 'reports.titles.summary',
          defaultMessage: 'Summary',
        })}
        surveyName={this.props.reportData.getIn(['header', 'surveyName'])}
      >
        <ReportViewedEventTracker page="insight - summary" />
        <QueryStatus
          queryStatus={queryStatus}
          path={this.getHomePath()}
          pageName="surveys"
        >
          <div
            dir={locale.direction}
            className={classNames(
              styles.container,
              styles.containerWithNewNavigation
            )}
          >
            {this.renderSummaryReportFactorScoresOrPrivacyNotification()}
            <InsufficientResults
              insufficientResponsesClassNames={styles.insufficientResult}
            >
              {this.renderCommentary()}
              {this.renderStrengthsAndOpportunities()}
              {this.renderCallToAction()}
              {this.renderFooter()}
            </InsufficientResults>
          </div>
        </QueryStatus>
      </WithReportPageTitle>
    );
  }
}

const IntlInjectedSummaryInsightReportApp = withReactIntl(
  SummaryInsightReportApp
);

function mapStateToProps(state: RootState) {
  const reportData = getReportDataState(state).get('reportData');
  const connectedHeaderContent = {
    ...reportData.get('header').toJS(),
    ...getHeaderState(state).toJS(),
  };

  return {
    headerContent: connectedHeaderContent,
    linkParams: getParamsForAppliedState(state),
  };
}

export const ConnectedSummaryInsightReportApp: React.ComponentType<ComponentProps> =
  connect<ReduxProps, DispatchProps, ComponentProps, RootState>(
    mapStateToProps,
    {
      gotoSinglePageAppPage,
    }
  )(withLocale(withPrint(IntlInjectedSummaryInsightReportApp)));

export default ConnectedSummaryInsightReportApp;
