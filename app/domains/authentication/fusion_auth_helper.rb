module Authentication
  module <PERSON><PERSON>uthHelper
    def fusionauth_jwt_post_signin_enabled?(user)
      FeatureFlags::Queries::ValueForContext.new.call(
        flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
        subdomain: user.account.subdomain,
        fallback_value: false
      )
    end

    def delete_fusionauth_entity(sid)
      delete_entity = Authentication::Commands::FusionAuthDeleteEntity.new
      result = delete_entity.call(entity_id: sid)

      unless result.success?
        Rails.logger.error("Failed to delete FusionAuth entity for masquerade session")
        Sentry.capture_exception(result.failure)
        raise "Failed to delete FusionAuth entity for masquerade session"
      end
    end

    def sign_out_fusionauth(user_id:, refresh_token:, global: false)
      sign_out_command = Authentication::Commands::FusionAuthSignOut.new
      result = sign_out_command.call(user_id: user_id, refresh_token: refresh_token, global: global)

      unless result.success?
        Rails.logger.error("Failed to sign out from FusionAuth")
        Sentry.capture_exception(result.failure)
        raise "Failed to sign out from FusionAuth"
      end
    end

    def fusionauth_passwordless_login(user)
      result = Authentication::Commands::FusionAuthPasswordlessLogin.new.call(
        account: user.account,
        email: user.email
      )

      if result.success?
        [result.value![:jwt], result.value![:refresh_token]]
      end
    end

    def get_subdomain_from_request()
      request.subdomains.first
    end
  end
end
