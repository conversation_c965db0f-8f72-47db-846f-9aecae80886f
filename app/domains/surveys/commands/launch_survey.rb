module Surveys
  module Commands
    class LaunchSurvey
      attr_reader :command_handler, :survey_period_id_generator, :launch_survey_mongo

      def initialize(
        command_handler: Domains::SurveyDesign::SurveyPeriod::LaunchCommandHandler.new,
        survey_period_id_generator: -> { SecureRandom.uuid },
        launch_survey_mongo: Surveys::Commands::LaunchSurvey::LaunchSurveyMongo.new
      )
        @command_handler = command_handler
        @survey_period_id_generator = survey_period_id_generator
        @launch_survey_mongo = launch_survey_mongo
      end

      def call(survey:, executor_user_id:, account_id:, correlation_id:, survey_import: false)
        Datadog.tracer.trace("SurveyLaunch", {service: "murmur", resource: "Surveys::Commands::LaunchSurvey"}) do |span|
          span.set_tag("survey_id", survey.aggregate_id)
          span.set_tag("correlation_id", correlation_id)
          span.set_tag("survey_import", survey_import)
          # [EARTH-93] - Currently, the importer is not able to handle event sourced launched surveys
          # as it tries to create response commands. This is a temporary hack where we keep surveys as event sourced
          # and launch as non event sourced until we find a good approach for Survey Importer to generate response commands
          update_event_sourced_launch(survey) if survey_import

          if survey.event_sourced_survey_launch
            # We need to check for and store the survey period on the Mongo model here
            # so that if this code runs multiple times we re-use the same
            # survey_period_id.
            if survey.survey_period_id.nil?
              survey.event_sourced_field_protection_bypassed = true
              survey.update_attributes!(survey_period_id: survey_period_id_generator.call)
            end

            launch_command = Domains::SurveyDesign::SurveyPeriod::LaunchCommand.new(
              aggregate_id: survey.survey_period_id,
              survey_id: survey.aggregate_id,
              launched_at: Time.now.utc
            )
            metadata = EventFramework::Event::Metadata.new(
              {
                user_id: executor_user_id,
                account_id: account_id,
                correlation_id: correlation_id
              }.compact
            )
            command_handler.call(
              command: launch_command,
              metadata: metadata
            )
          else
            launch_survey_mongo.call(survey: survey)
          end
        end
      end

      private

      def update_event_sourced_launch(survey)
        survey.event_sourced_survey_launch = false
        survey.save!
      end
    end
  end
end
